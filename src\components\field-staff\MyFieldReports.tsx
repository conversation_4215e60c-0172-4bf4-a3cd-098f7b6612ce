import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { 
  FileText, 
  Eye, 
  Search, 
  Calendar,
  Clock,
  MapPin,
  Users,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { useFieldReports } from '@/hooks/field-staff/useFieldReports';
import { useAuth } from '@/hooks/useAuth';
import { format } from 'date-fns';
import FieldReportDetailsModal from './FieldReportDetailsModal';

const MyFieldReports: React.FC = () => {
  const [selectedReportId, setSelectedReportId] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [dateFilter, setDateFilter] = useState<string>('');

  const { profile } = useAuth();
  const { data: reports, isLoading, error } = useFieldReports({
    staffId: profile?.id,
    dateFrom: dateFilter || undefined,
    limit: 50
  });

  // Filter reports based on search term
  const filteredReports = reports?.filter(report => 
    report.school_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    report.activity_type.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const formatActivityType = (activityType: string) => {
    return activityType.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const getActivityTypeBadgeColor = (activityType: string) => {
    switch (activityType) {
      case 'round_table_session':
        return 'bg-blue-100 text-blue-800';
      case 'school_visit':
        return 'bg-green-100 text-green-800';
      case 'leadership_training':
        return 'bg-purple-100 text-purple-800';
      case 'community_engagement':
        return 'bg-orange-100 text-orange-800';
      case 'assessment':
        return 'bg-yellow-100 text-yellow-800';
      case 'meeting':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-8 text-red-500">
            <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Error loading field reports</p>
            <p className="text-sm mt-2">Please try again later</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search by school or activity type..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex gap-2">
          <Input
            type="date"
            value={dateFilter}
            onChange={(e) => setDateFilter(e.target.value)}
            className="w-auto"
          />
          {dateFilter && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setDateFilter('')}
            >
              Clear
            </Button>
          )}
        </div>
      </div>

      {/* Reports Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            My Field Reports ({filteredReports.length})
          </CardTitle>
          <CardDescription>
            Your submitted field reports from recent visits
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredReports.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No field reports found</p>
              <p className="text-sm mt-2">
                {searchTerm || dateFilter
                  ? 'Try adjusting your filters'
                  : 'Your field reports will appear here after you submit them during check-out'
                }
              </p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>School</TableHead>
                    <TableHead>Activity Type</TableHead>
                    <TableHead>Time</TableHead>
                    <TableHead>Sessions</TableHead>
                    <TableHead>Students</TableHead>
                    <TableHead>Follow-up</TableHead>
                    <TableHead className="w-[100px]">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredReports.map(report => (
                    <TableRow key={report.id} className="hover:bg-gray-50">
                      <TableCell>
                        {report.report_date ? format(new Date(report.report_date), 'MMM dd, yyyy') : 'N/A'}
                      </TableCell>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4 text-gray-400" />
                          {report.school_name}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getActivityTypeBadgeColor(report.activity_type)}>
                          {formatActivityType(report.activity_type)}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-sm">
                        {report.attendance_check_in_time && report.attendance_check_out_time ? (
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3 text-gray-400" />
                            {format(new Date(report.attendance_check_in_time), 'HH:mm')} - {format(new Date(report.attendance_check_out_time), 'HH:mm')}
                          </div>
                        ) : report.attendance_check_in_time ? (
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3 text-gray-400" />
                            {format(new Date(report.attendance_check_in_time), 'HH:mm')} - Active
                          </div>
                        ) : (
                          'N/A'
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3 text-gray-400" />
                          {report.round_table_sessions_count || 0}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Users className="h-3 w-3 text-gray-400" />
                          {report.total_students_attended || 0}
                        </div>
                      </TableCell>
                      <TableCell>
                        {report.follow_up_required ? (
                          <div className="flex items-center gap-1 text-orange-600">
                            <XCircle className="h-3 w-3" />
                            Required
                          </div>
                        ) : (
                          <div className="flex items-center gap-1 text-green-600">
                            <CheckCircle className="h-3 w-3" />
                            Complete
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setSelectedReportId(report.id)}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          View
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Report Details Modal */}
      {selectedReportId && (
        <FieldReportDetailsModal
          reportId={selectedReportId}
          isOpen={!!selectedReportId}
          onClose={() => setSelectedReportId(null)}
        />
      )}
    </div>
  );
};

export default MyFieldReports;
