import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
  FileText,
  Eye,
  Calendar,
  MapPin,
  User,
  Clock,
  Search,
  Filter,
  Loader2
} from 'lucide-react';
import { useFieldReports } from '@/hooks/field-staff/useFieldReports';
import { useSchools } from '@/hooks/useSchools';
import { format } from 'date-fns';
import FieldReportDetailsModal from './FieldReportDetailsModal';

const FieldReportsList: React.FC = () => {
  const [selectedReportId, setSelectedReportId] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSchool, setSelectedSchool] = useState<string>('');
  const [dateFilter, setDateFilter] = useState<string>('');

  const { data: schools } = useSchools();
  const { data: reports, isLoading, error } = useFieldReports({
    schoolId: selectedSchool || undefined,
    dateFrom: dateFilter || undefined,
    limit: 50
  });

  // Filter reports based on search term
  const filteredReports = reports?.filter(report => 
    report.staff_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    report.school_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    report.activity_type.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const getActivityTypeBadgeColor = (activityType: string) => {
    switch (activityType) {
      case 'leadership_training':
        return 'bg-blue-100 text-blue-800';
      case 'school_visit':
        return 'bg-green-100 text-green-800';
      case 'teacher_training':
        return 'bg-purple-100 text-purple-800';
      case 'student_mentoring':
        return 'bg-orange-100 text-orange-800';
      case 'community_engagement':
        return 'bg-pink-100 text-pink-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatActivityType = (activityType: string) => {
    return activityType.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading field reports...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-red-600">
            <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Failed to load field reports</p>
            <p className="text-sm text-gray-500 mt-2">{error.message}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <div className="space-y-6">
        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filter Reports
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search by staff, school, or activity..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">School</label>
                <Select value={selectedSchool} onValueChange={setSelectedSchool}>
                  <SelectTrigger>
                    <SelectValue placeholder="All schools" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All schools</SelectItem>
                    {schools?.map(school => (
                      <SelectItem key={school.id} value={school.id}>
                        {school.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Date From</label>
                <Input
                  type="date"
                  value={dateFilter}
                  onChange={(e) => setDateFilter(e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Reports Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Field Reports ({filteredReports.length})
            </CardTitle>
            <CardDescription>
              Recent field reports submitted by staff during check-out
            </CardDescription>
          </CardHeader>
          <CardContent>
            {filteredReports.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No field reports found</p>
                <p className="text-sm mt-2">
                  {searchTerm || selectedSchool || dateFilter
                    ? 'Try adjusting your filters'
                    : 'Field reports will appear here when staff submit them during check-out'
                  }
                </p>
              </div>
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Staff Member</TableHead>
                      <TableHead>School</TableHead>
                      <TableHead>Activity Type</TableHead>
                      <TableHead>Time</TableHead>
                      <TableHead>Sessions</TableHead>
                      <TableHead>Students</TableHead>
                      <TableHead>Follow-up</TableHead>
                      <TableHead className="w-[100px]">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredReports.map(report => (
                      <TableRow key={report.id} className="hover:bg-gray-50">
                        <TableCell>
                          {report.report_date ? format(new Date(report.report_date), 'MMM dd, yyyy') : 'N/A'}
                        </TableCell>
                        <TableCell className="font-medium">
                          {report.staff_name}
                        </TableCell>
                        <TableCell>
                          {report.school_name}
                        </TableCell>
                        <TableCell>
                          <Badge className={getActivityTypeBadgeColor(report.activity_type)}>
                            {formatActivityType(report.activity_type)}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-sm">
                          {report.attendance_check_in_time && report.attendance_check_out_time ? (
                            `${format(new Date(report.attendance_check_in_time), 'HH:mm')} - ${format(new Date(report.attendance_check_out_time), 'HH:mm')}`
                          ) : report.attendance_check_in_time ? (
                            `${format(new Date(report.attendance_check_in_time), 'HH:mm')} - Active`
                          ) : (
                            'N/A'
                          )}
                        </TableCell>
                        <TableCell>
                          {report.round_table_sessions || 0}
                        </TableCell>
                        <TableCell>
                          {report.total_students || 0}
                        </TableCell>
                        <TableCell>
                          <Badge variant={report.follow_up_required ? "destructive" : "secondary"}>
                            {report.follow_up_required ? 'Required' : 'None'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => setSelectedReportId(report.id)}
                                  className="h-8 w-8 p-0"
                                >
                                  <Eye className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>View Details</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Report Details Modal */}
      {selectedReportId && (
        <FieldReportDetailsModal
          reportId={selectedReportId}
          isOpen={!!selectedReportId}
          onClose={() => setSelectedReportId(null)}
        />
      )}
    </>
  );
};

export default FieldReportsList;
