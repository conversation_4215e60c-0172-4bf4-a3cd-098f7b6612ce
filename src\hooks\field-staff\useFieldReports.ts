import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Database } from '@/types/database.types';

type FieldReport = Database['public']['Tables']['field_reports']['Row'] & {
  staff_name: string;
  school_name: string;
  attendance_check_in_time: string;
  attendance_check_out_time: string;
};

interface UseFieldReportsOptions {
  staffId?: string;
  schoolId?: string;
  dateFrom?: string;
  dateTo?: string;
  limit?: number;
}

export const useFieldReports = (options: UseFieldReportsOptions = {}) => {
  return useQuery({
    queryKey: ['field-reports', options],
    queryFn: async (): Promise<FieldReport[]> => {
      let query = supabase
        .from('field_reports')
        .select(`
          *,
          profiles!field_reports_staff_id_fkey(name),
          schools!field_reports_school_id_fkey(name),
          field_staff_attendance!field_reports_attendance_id_fkey(
            check_in_time,
            check_out_time
          )
        `)
        .order('report_date', { ascending: false });

      // Apply filters
      if (options.staffId) {
        query = query.eq('staff_id', options.staffId);
      }

      if (options.schoolId) {
        query = query.eq('school_id', options.schoolId);
      }

      if (options.dateFrom) {
        query = query.gte('report_date', options.dateFrom);
      }

      if (options.dateTo) {
        query = query.lte('report_date', options.dateTo);
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      const { data, error } = await query;

      if (error) {
        throw new Error(`Failed to fetch field reports: ${error.message}`);
      }

      // Get auth users to fetch email addresses
      const { data: authUsers } = await supabase.auth.admin.listUsers();

      // Transform the data to flatten the nested objects
      return (data || []).map(report => {
        const authUser = authUsers?.users?.find(user => user.id === report.staff_id);
        return {
          ...report,
          staff_name: authUser?.email || report.profiles?.name || 'Unknown Staff',
          school_name: report.schools?.name || 'Unknown School',
          attendance_check_in_time: report.field_staff_attendance?.check_in_time || '',
          attendance_check_out_time: report.field_staff_attendance?.check_out_time || '',
        };
      });
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useFieldReport = (reportId: string) => {
  return useQuery({
    queryKey: ['field-report', reportId],
    queryFn: async (): Promise<FieldReport> => {
      const { data, error } = await supabase
        .from('field_reports')
        .select(`
          *,
          profiles!field_reports_staff_id_fkey(name),
          schools!field_reports_school_id_fkey(name),
          field_staff_attendance!field_reports_attendance_id_fkey(
            check_in_time,
            check_out_time,
            check_in_address,
            check_out_address
          )
        `)
        .eq('id', reportId)
        .single();

      if (error) {
        throw new Error(`Failed to fetch field report: ${error.message}`);
      }

      if (!data) {
        throw new Error('Field report not found');
      }

      // Get auth user to fetch email address
      const { data: authUsers } = await supabase.auth.admin.listUsers();
      const authUser = authUsers?.users?.find(user => user.id === data.staff_id);

      // Transform the data to flatten the nested objects
      return {
        ...data,
        staff_name: authUser?.email || data.profiles?.name || 'Unknown Staff',
        school_name: data.schools?.name || 'Unknown School',
        attendance_check_in_time: data.field_staff_attendance?.check_in_time || '',
        attendance_check_out_time: data.field_staff_attendance?.check_out_time || '',
      };
    },
    enabled: !!reportId,
  });
};
