import React, { useState, useMemo } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  MapPin,
  Calendar,
  Clock,
  Users,
  CheckCircle,
  Navigation,
  Plus,
  Timer,
  Target,
  BookOpen,
  Eye,
  FileText,
  BarChart3
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { useUnifiedCheckInStatus } from '@/hooks/attendance/useUnifiedCheckInStatus';
import { useAttendanceSessions } from '@/hooks/attendance/useAttendanceSessions';
import { Database } from '@/types/database.types';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';

type AttendanceSession = Database['public']['Tables']['attendance_sessions']['Row'] & {
  school?: { name: string };
  facilitator?: { name: string };
  student_attendance?: Database['public']['Tables']['student_attendance']['Row'][];
};
import GPSCheckIn from './GPSCheckIn';
import SessionManagement from './SessionManagement';
import CreateSessionDialog from './CreateSessionDialog';
import FieldStaffCheckIn from '../field-staff/FieldStaffCheckIn';
import FieldStaffCheckOut from '../field-staff/FieldStaffCheckOut';
import MyFieldReports from '../field-staff/MyFieldReports';
import ConsolidatedStaffReports from './ConsolidatedStaffReports';
import UnifiedAttendanceAnalytics from './UnifiedAttendanceAnalytics';

const UnifiedFieldVisits = () => {
  const { profile } = useAuth();
  const [activeTab, setActiveTab] = useState('check-in');
  
  // Check current status using unified hook
  const { data: unifiedStatus, isLoading: statusLoading } = useUnifiedCheckInStatus();

  // Memoize today's date range to prevent infinite re-renders
  const todayDateRange = useMemo(() => {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Start of day
    const endOfDay = new Date(today);
    endOfDay.setHours(23, 59, 59, 999); // End of day
    return {
      start: today,
      end: endOfDay
    };
  }, []); // Empty dependency array since we want today's date to be stable during the component lifecycle

  const { data: todaySessions } = useAttendanceSessions(undefined, todayDateRange);

  // Type cast sessions data to ensure proper typing
  const typedSessions = todaySessions as AttendanceSession[] | undefined;

  // Role-based permissions
  const isFieldStaff = profile?.role === 'field_staff';
  const canManageSessions = profile?.role === 'admin' || profile?.role === 'program_officer';

  // Determine current status from unified source
  const isCheckedIn = unifiedStatus?.isCheckedIn ?? false;
  const hasActiveSessions = typedSessions && typedSessions.length > 0;

  // Get status info for header
  const getStatusInfo = () => {
    if (isCheckedIn && unifiedStatus) {
      const location = unifiedStatus.schoolName || 'Unknown location';
      const timeStr = unifiedStatus.checkInTime ? new Date(unifiedStatus.checkInTime).toLocaleTimeString() : '';
      return {
        status: 'checked-in',
        message: `Checked in at ${location} ${timeStr ? `at ${timeStr}` : ''}`,
        color: 'bg-green-100 text-green-800'
      };
    }

    if (hasActiveSessions) {
      return {
        status: 'sessions-available',
        message: `${todaySessions?.length} session(s) scheduled for today`,
        color: 'bg-blue-100 text-blue-800'
      };
    }

    return {
      status: 'ready',
      message: 'Ready for field visits',
      color: 'bg-gray-100 text-gray-800'
    };
  };

  const statusInfo = getStatusInfo();

  // Tab configuration based on status (unified for all roles)
  const getAvailableTabs = () => {
    const tabs = [];

    // Check-in/Check-out tab (unified for all roles)
    tabs.push({
      id: 'check-in',
      label: isCheckedIn ? 'Check Out & Report' : 'Check In',
      icon: isCheckedIn ? CheckCircle : MapPin,
      badge: isCheckedIn ? 'Active' : null
    });

    // Field Reports tab for field staff, Sessions tab for others
    if (isFieldStaff) {
      tabs.push({
        id: 'field-reports',
        label: 'Field Reports',
        icon: FileText,
        badge: null
      });
    } else {
      tabs.push({
        id: 'sessions',
        label: 'Sessions',
        icon: Calendar,
        badge: hasActiveSessions ? typedSessions?.length.toString() : null
      });
    }

    // Staff Reports tab (for admin and program officers only)
    if (canManageSessions) {
      tabs.push({
        id: 'staff-reports',
        label: 'Staff Reports',
        icon: Users,
        badge: null
      });
    }

    // Analytics tab (for admin and program officers only)
    if (canManageSessions) {
      tabs.push({
        id: 'analytics',
        label: 'Analytics',
        icon: BarChart3,
        badge: null
      });
    }

    return tabs;
  };

  const availableTabs = getAvailableTabs();

  return (
    <PageLayout>
      <PageHeader
        title={isFieldStaff ? 'Field Visits' : 'Field Visit Management'}
        description={isFieldStaff 
          ? 'Check in/out and manage your field visit sessions'
          : 'GPS check-in and session management for field visits'
        }
        icon={Navigation}
      >
        {/* Status Badge */}
        <Badge className={statusInfo.color}>
          {statusInfo.message}
        </Badge>
      </PageHeader>

      <ContentCard>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          {/* Tab Navigation */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
            <TabsList className={`grid w-full ${availableTabs.length <= 2 ? 'grid-cols-2' : availableTabs.length <= 3 ? 'grid-cols-3' : availableTabs.length <= 4 ? 'grid-cols-4' : 'grid-cols-5'} sm:w-auto`}>
              {availableTabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <TabsTrigger key={tab.id} value={tab.id} className="flex items-center gap-2">
                    <Icon className="h-4 w-4" />
                    <span>{tab.label}</span>
                    {tab.badge && (
                      <Badge variant="secondary" className="ml-1">
                        {tab.badge}
                      </Badge>
                    )}
                  </TabsTrigger>
                );
              })}
            </TabsList>
          </div>

          {/* Tab Content */}
          <TabsContent value="check-in" className="mt-0">
            {/* Unified Check-in/Check-out interface for all roles */}
            <div className="space-y-6">
              {!isFieldStaff && (
                <Alert>
                  <Navigation className="h-4 w-4" />
                  <AlertDescription>
                    Use the comprehensive check-in system to track field visits and submit detailed reports.
                  </AlertDescription>
                </Alert>
              )}

              {isCheckedIn ? (
                // Show check-out interface for all roles
                <FieldStaffCheckOut />
              ) : (
                // Show check-in interface for all roles
                <FieldStaffCheckIn />
              )}
            </div>
          </TabsContent>

          <TabsContent value="field-reports" className="mt-0">
            <MyFieldReports />
          </TabsContent>

          <TabsContent value="sessions" className="mt-0">
            {/* Sessions tab is only for admin and program officers */}
            <div className="space-y-6">
              {/* Session Overview Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      Today's Sessions
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{typedSessions?.length || 0}</div>
                    <p className="text-xs text-muted-foreground">
                      Scheduled for today
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      Active Now
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {typedSessions?.filter(s => {
                        const now = new Date();
                        const sessionStart = new Date(`${s.session_date}T${s.start_time}`);
                        const sessionEnd = s.end_time
                          ? new Date(`${s.session_date}T${s.end_time}`)
                          : new Date(sessionStart.getTime() + (s.planned_duration_minutes || 60) * 60000);
                        return now >= sessionStart && now <= sessionEnd;
                      }).length || 0}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Currently running
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      Total Attendance
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {typedSessions?.reduce((total, session) =>
                        total + (session.student_attendance?.length || 0), 0) || 0}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Students today
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* Session Management Component */}
              <SessionManagement />
            </div>
          </TabsContent>

          {/* Staff Reports Tab - Admin and Program Officer only */}
          {canManageSessions && (
            <TabsContent value="staff-reports" className="mt-0">
              <div className="space-y-6">
                <Alert>
                  <Users className="h-4 w-4" />
                  <AlertDescription>
                    Comprehensive staff reporting dashboard for timesheets, field reports, and notifications.
                  </AlertDescription>
                </Alert>

                <ConsolidatedStaffReports />
              </div>
            </TabsContent>
          )}

          {/* Analytics Tab - Admin and Program Officer only */}
          {canManageSessions && (
            <TabsContent value="analytics" className="mt-0">
              <div className="space-y-6">
                <Alert>
                  <BarChart3 className="h-4 w-4" />
                  <AlertDescription>
                    Advanced analytics for field staff productivity, school visit patterns, and attendance trends.
                  </AlertDescription>
                </Alert>

                <UnifiedAttendanceAnalytics />
              </div>
            </TabsContent>
          )}
        </Tabs>
      </ContentCard>
    </PageLayout>
  );
};

export default UnifiedFieldVisits;
